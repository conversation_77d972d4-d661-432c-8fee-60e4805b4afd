package com.jingzhenjili.vo;

import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 用户信息VO
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
@Data
public class UserVO {

    /**
     * 用户ID
     */
    private Long id;

    /**
     * 用户名
     */
    private String username;

    /**
     * 邮箱
     */
    private String email;

    /**
     * 手机号
     */
    private String phone;

    /**
     * 昵称
     */
    private String nickname;

    /**
     * 头像URL
     */
    private String avatar;

    /**
     * 性别：0-未知，1-男，2-女
     */
    private Integer gender;

    /**
     * 性别描述
     */
    private String genderDesc;

    /**
     * 生日
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate birthday;
    
    /**
     * 个人简介
     */
    private String bio;

    /**
     * 状态：0-禁用，1-启用
     */
    private Integer status;

    /**
     * 状态描述
     */
    private String statusDesc;

    /**
     * 最后登录时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime lastLoginTime;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createdTime;

    /**
     * 总积分
     */
    private Integer totalPoints;

    /**
     * 好友数量
     */
    private Integer friendCount;

    /**
     * 完成任务数量
     */
    private Integer completedTaskCount;

    /**
     * 获胜次数
     */
    private Integer winCount;

    // 兼容前端字段名的getter方法
    public Integer getCompletedTasks() {
        return this.completedTaskCount;
    }

    // 设置性别描述
    public void setGender(Integer gender) {
        this.gender = gender;
        if (gender != null) {
            switch (gender) {
                case 1:
                    this.genderDesc = "男";
                    break;
                case 2:
                    this.genderDesc = "女";
                    break;
                default:
                    this.genderDesc = "未知";
                    break;
            }
        }
    }

    // 设置状态描述
    public void setStatus(Integer status) {
        this.status = status;
        if (status != null) {
            this.statusDesc = status == 1 ? "启用" : "禁用";
        }
    }
}
