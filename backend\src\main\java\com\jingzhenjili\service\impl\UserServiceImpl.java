package com.jingzhenjili.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jingzhenjili.common.ResultCode;
import com.jingzhenjili.dto.LoginRequest;
import com.jingzhenjili.dto.RegisterRequest;
import com.jingzhenjili.dto.UserUpdateRequest;
import com.jingzhenjili.entity.User;
import com.jingzhenjili.exception.BusinessException;
import com.jingzhenjili.mapper.UserMapper;
import com.jingzhenjili.mapper.RoleMapper;
import com.jingzhenjili.mapper.PermissionMapper;
import com.jingzhenjili.mapper.UserRoleMapper;
import com.jingzhenjili.mapper.UserPointsMapper;
import com.jingzhenjili.mapper.FriendshipMapper;
import com.jingzhenjili.entity.Role;
import com.jingzhenjili.entity.Permission;
import com.jingzhenjili.entity.UserRole;
import com.jingzhenjili.service.UserService;
import com.jingzhenjili.util.JwtUtil;
import com.jingzhenjili.vo.LoginResponse;
import com.jingzhenjili.vo.UserVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 用户服务实现类
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
@Slf4j
@Service
public class UserServiceImpl extends ServiceImpl<UserMapper, User> implements UserService {

    @Autowired
    private UserMapper userMapper;

    @Autowired
    private RoleMapper roleMapper;

    @Autowired
    private PermissionMapper permissionMapper;

    @Autowired
    private UserRoleMapper userRoleMapper;

    @Autowired
    private UserPointsMapper userPointsMapper;

    @Autowired
    private FriendshipMapper friendshipMapper;

    @Autowired
    private AuthenticationManager authenticationManager;

    @Autowired
    private JwtUtil jwtUtil;

    @Autowired
    private PasswordEncoder passwordEncoder;

    @Override
    @Transactional
    public LoginResponse login(LoginRequest loginRequest) {
        try {
            // 进行身份验证
            Authentication authentication = authenticationManager.authenticate(
                    new UsernamePasswordAuthenticationToken(
                            loginRequest.getUsername(),
                            loginRequest.getPassword()
                    )
            );

            // 获取用户信息
            User user = findByUsername(loginRequest.getUsername());
            if (user == null) {
                throw new BusinessException(ResultCode.USER_NOT_FOUND);
            }

            if (user.getStatus() == 0) {
                throw new BusinessException(ResultCode.USER_DISABLED);
            }

            // 生成JWT Token
            String token = jwtUtil.generateToken(user.getId(), user.getUsername());
            Long expiresIn = jwtUtil.getTokenRemainingTime(token);

            // 更新最后登录时间
            updateLastLoginTime(user.getId());

            // 构建用户信息VO
            UserVO userVO = convertToUserVO(user);

            return new LoginResponse(token, expiresIn, userVO);

        } catch (Exception e) {
            log.error("用户登录失败: {}", e.getMessage());
            if (e instanceof BusinessException) {
                throw e;
            }
            throw new BusinessException(ResultCode.PASSWORD_ERROR);
        }
    }

    @Override
    @Transactional
    public UserVO register(RegisterRequest registerRequest) {
        // 验证确认密码
        if (!registerRequest.getPassword().equals(registerRequest.getConfirmPassword())) {
            throw new BusinessException(ResultCode.PARAM_ERROR, "两次输入的密码不一致");
        }

        // 检查用户名是否存在
        if (checkUsernameExists(registerRequest.getUsername())) {
            throw new BusinessException(ResultCode.USERNAME_EXISTS);
        }

        // 检查邮箱是否存在
        if (checkEmailExists(registerRequest.getEmail())) {
            throw new BusinessException(ResultCode.EMAIL_EXISTS);
        }

        // 检查手机号是否存在（如果提供了手机号）
        if (StringUtils.hasText(registerRequest.getPhone()) && checkPhoneExists(registerRequest.getPhone())) {
            throw new BusinessException(ResultCode.PHONE_EXISTS);
        }

        // 创建用户
        User user = new User();
        user.setUsername(registerRequest.getUsername());
        user.setPassword(passwordEncoder.encode(registerRequest.getPassword()));
        user.setEmail(registerRequest.getEmail());
        user.setPhone(registerRequest.getPhone());
        user.setNickname(StringUtils.hasText(registerRequest.getNickname()) ? 
                registerRequest.getNickname() : registerRequest.getUsername());
        user.setGender(registerRequest.getGender() != null ? registerRequest.getGender() : 0);
        user.setStatus(1);

        // 保存用户
        if (!save(user)) {
            throw new BusinessException(ResultCode.ERROR, "用户注册失败");
        }

        log.info("用户注册成功: {}", user.getUsername());
        return convertToUserVO(user);
    }

    @Override
    public User findByUsername(String username) {
        return userMapper.findByUsername(username);
    }

    @Override
    public User findByEmail(String email) {
        return userMapper.findByEmail(email);
    }

    @Override
    public User findByPhone(String phone) {
        return userMapper.findByPhone(phone);
    }

    @Override
    public UserVO getUserInfo(Long userId) {
        User user = getById(userId);
        if (user == null) {
            throw new BusinessException(ResultCode.USER_NOT_FOUND);
        }
        return convertToUserVO(user);
    }

    @Override
    @Transactional
    public UserVO updateUserInfo(Long userId, UserUpdateRequest updateRequest) {
        User user = getById(userId);
        if (user == null) {
            throw new BusinessException(ResultCode.USER_NOT_FOUND);
        }

        // 检查邮箱是否被其他用户使用
        if (StringUtils.hasText(updateRequest.getEmail()) && !updateRequest.getEmail().equals(user.getEmail())) {
            User existUser = findByEmail(updateRequest.getEmail());
            if (existUser != null && !existUser.getId().equals(userId)) {
                throw new BusinessException(ResultCode.EMAIL_EXISTS);
            }
        }

        // 检查手机号是否被其他用户使用
        if (StringUtils.hasText(updateRequest.getPhone()) && !updateRequest.getPhone().equals(user.getPhone())) {
            User existUser = findByPhone(updateRequest.getPhone());
            if (existUser != null && !existUser.getId().equals(userId)) {
                throw new BusinessException(ResultCode.PHONE_EXISTS);
            }
        }

        // 更新用户信息
        BeanUtils.copyProperties(updateRequest, user, "id", "username", "password", "status", "createdTime");
        
        if (!updateById(user)) {
            throw new BusinessException(ResultCode.ERROR, "更新用户信息失败");
        }

        log.info("用户信息更新成功: {}", user.getUsername());
        return convertToUserVO(user);
    }

    @Override
    @Transactional
    public Boolean changePassword(Long userId, String oldPassword, String newPassword) {
        User user = getById(userId);
        if (user == null) {
            throw new BusinessException(ResultCode.USER_NOT_FOUND);
        }

        // 验证旧密码
        if (!passwordEncoder.matches(oldPassword, user.getPassword())) {
            throw new BusinessException(ResultCode.OLD_PASSWORD_ERROR);
        }

        // 更新密码
        user.setPassword(passwordEncoder.encode(newPassword));
        boolean result = updateById(user);

        if (result) {
            log.info("用户密码修改成功: {}", user.getUsername());
        }

        return result;
    }

    @Override
    public List<UserVO> searchUsers(String keyword, Integer limit) {
        if (limit == null || limit <= 0) {
            limit = 10;
        }
        
        List<User> users = userMapper.searchUsers(keyword, limit);
        return users.stream().map(this::convertToUserVO).collect(Collectors.toList());
    }

    @Override
    @Transactional
    public void updateLastLoginTime(Long userId) {
        User user = new User();
        user.setId(userId);
        user.setLastLoginTime(LocalDateTime.now());
        updateById(user);
    }

    @Override
    public Boolean checkUsernameExists(String username) {
        return findByUsername(username) != null;
    }

    @Override
    public Boolean checkEmailExists(String email) {
        return findByEmail(email) != null;
    }

    @Override
    public Boolean checkPhoneExists(String phone) {
        return StringUtils.hasText(phone) && findByPhone(phone) != null;
    }

    // ==================== 管理员相关方法实现 ====================

    @Override
    public LoginResponse adminLogin(LoginRequest loginRequest) {
        // 验证用户身份
        Authentication authentication = authenticationManager.authenticate(
                new UsernamePasswordAuthenticationToken(
                        loginRequest.getUsername(),
                        loginRequest.getPassword()
                )
        );

        // 获取用户信息
        User user = findByUsername(loginRequest.getUsername());
        if (user == null) {
            throw new BusinessException(ResultCode.USER_NOT_FOUND);
        }

        // 检查是否为管理员（这里简单判断，实际项目中应该有角色表）
        // 假设用户名为admin或者有特定标识的为管理员
        if (!isAdmin(user)) {
            throw new BusinessException(ResultCode.UNAUTHORIZED, "非管理员用户");
        }

        // 更新最后登录时间
        updateLastLoginTime(user.getId());

        // 生成JWT token
        String token = jwtUtil.generateToken(user.getId(), user.getUsername());

        // 构建响应
        LoginResponse response = new LoginResponse();
        response.setAccessToken(token);
        response.setUserInfo(convertToUserVO(user));
        response.setExpiresIn(7200L); // 2小时过期

        return response;
    }

    @Override
    public IPage<UserVO> getAdminUserPage(Integer page, Integer size, String keyword, Integer status, String startDate, String endDate) {
        Page<User> pageParam = new Page<>(page, size);
        QueryWrapper<User> queryWrapper = new QueryWrapper<>();

        // 关键词搜索
        if (StringUtils.hasText(keyword)) {
            queryWrapper.and(wrapper -> wrapper
                    .like("username", keyword)
                    .or()
                    .like("nickname", keyword)
                    .or()
                    .like("email", keyword)
            );
        }

        // 状态筛选
        if (status != null) {
            queryWrapper.eq("status", status);
        }

        // 时间范围筛选
        if (StringUtils.hasText(startDate)) {
            queryWrapper.ge("created_time", startDate);
        }
        if (StringUtils.hasText(endDate)) {
            queryWrapper.le("created_time", endDate);
        }

        // 按创建时间倒序
        queryWrapper.orderByDesc("created_time");

        IPage<User> userPage = userMapper.selectPage(pageParam, queryWrapper);

        // 转换为VO
        IPage<UserVO> userVOPage = new Page<>();
        BeanUtils.copyProperties(userPage, userVOPage);
        userVOPage.setRecords(userPage.getRecords().stream()
                .map(this::convertToUserVO)
                .collect(Collectors.toList()));

        return userVOPage;
    }

    @Override
    @Transactional
    public Boolean updateUserStatus(Long userId, Integer status) {
        User user = getById(userId);
        if (user == null) {
            throw new BusinessException(ResultCode.USER_NOT_FOUND);
        }

        user.setStatus(status);
        user.setUpdatedTime(LocalDateTime.now());

        return updateById(user);
    }

    @Override
    @Transactional
    public Boolean resetUserPassword(Long userId) {
        User user = getById(userId);
        if (user == null) {
            throw new BusinessException(ResultCode.USER_NOT_FOUND);
        }

        // 重置为默认密码
        String defaultPassword = "123456";
        user.setPassword(passwordEncoder.encode(defaultPassword));
        user.setUpdatedTime(LocalDateTime.now());

        return updateById(user);
    }

    @Override
    @Transactional
    public Boolean deleteUser(Long userId) {
        User user = getById(userId);
        if (user == null) {
            throw new BusinessException(ResultCode.USER_NOT_FOUND);
        }

        // 软删除：更新状态为已删除
        user.setStatus(3); // 假设3表示已删除
        user.setUpdatedTime(LocalDateTime.now());

        return updateById(user);
    }

    @Override
    public Map<String, Object> getUserStats() {
        Map<String, Object> stats = new HashMap<>();

        // 总用户数
        long totalCount = count();
        stats.put("totalCount", totalCount);

        // 活跃用户数（最近30天登录的用户）
        QueryWrapper<User> activeWrapper = new QueryWrapper<>();
        activeWrapper.ge("last_login_time", LocalDateTime.now().minusDays(30));
        long activeCount = count(activeWrapper);
        stats.put("activeCount", activeCount);

        // 今日新增用户
        QueryWrapper<User> todayWrapper = new QueryWrapper<>();
        todayWrapper.ge("created_time", LocalDateTime.now().toLocalDate().atStartOfDay());
        long todayCount = count(todayWrapper);
        stats.put("todayCount", todayCount);

        // 本月新增用户
        QueryWrapper<User> monthWrapper = new QueryWrapper<>();
        monthWrapper.ge("created_time", LocalDateTime.now().withDayOfMonth(1).toLocalDate().atStartOfDay());
        long monthCount = count(monthWrapper);
        stats.put("monthCount", monthCount);

        return stats;
    }

    @Override
    public Boolean isAdmin(User user) {
        if (user == null) {
            return false;
        }

        // 方式1：基于用户类型判断
        if ("ADMIN".equals(user.getUserType())) {
            return true;
        }

        // 方式2：基于用户名判断（兼容性）
        if ("admin".equals(user.getUsername()) || "superadmin".equals(user.getUsername())) {
            return true;
        }

        // 方式3：基于角色判断
        try {
            List<Role> roles = roleMapper.findRolesByUserId(user.getId());
            for (Role role : roles) {
                if ("SUPER_ADMIN".equals(role.getRoleCode()) || "ADMIN".equals(role.getRoleCode())) {
                    return true;
                }
            }
        } catch (Exception e) {
            log.warn("查询用户角色失败: {}", e.getMessage());
        }

        return false;
    }

    /**
     * 检查用户是否为超级管理员
     */
    public Boolean isSuperAdmin(User user) {
        if (user == null) {
            return false;
        }

        return "superadmin".equals(user.getUsername()) ||
               (user.getEmail() != null && user.getEmail().equals("<EMAIL>"));
    }

    /**
     * 获取用户角色
     */
    public String getUserRole(User user) {
        if (user == null) {
            return "guest";
        }

        if (isSuperAdmin(user)) {
            return "super_admin";
        } else if (isAdmin(user)) {
            return "admin";
        } else {
            return "user";
        }
    }

    @Override
    public UserVO convertToUserVO(User user) {
        UserVO userVO = new UserVO();
        BeanUtils.copyProperties(user, userVO);

        // 设置性别和状态描述
        userVO.setGender(user.getGender());
        userVO.setStatus(user.getStatus());

        // 设置统计信息（积分、好友数量、完成任务数量、获胜次数）
        try {
            // 获取用户总积分
            Integer totalPoints = userPointsMapper.getUserTotalPoints(user.getId());
            userVO.setTotalPoints(totalPoints != null ? totalPoints : 0);

            // 获取好友数量
            Integer friendCount = friendshipMapper.getFriendCount(user.getId());
            userVO.setFriendCount(friendCount != null ? friendCount : 0);

            // 获取完成任务数量
            Integer completedTaskCount = userMapper.getUserCompletedTaskCount(user.getId());
            userVO.setCompletedTaskCount(completedTaskCount != null ? completedTaskCount : 0);

            // 获取竞争胜利次数
            Integer winCount = userMapper.getUserWinCount(user.getId());
            userVO.setWinCount(winCount != null ? winCount : 0);

            log.debug("用户{}统计信息: 积分={}, 好友={}, 完成任务={}, 胜利次数={}",
                    user.getId(), totalPoints, friendCount, completedTaskCount, winCount);
        } catch (Exception e) {
            log.error("获取用户{}统计信息失败: {}", user.getId(), e.getMessage());
            // 如果查询失败，设置默认值
            userVO.setTotalPoints(0);
            userVO.setFriendCount(0);
            userVO.setCompletedTaskCount(0);
            userVO.setWinCount(0);
        }

        return userVO;
    }

    @Override
    public Boolean hasPermission(Long userId, String permissionCode) {
        if (userId == null || !StringUtils.hasText(permissionCode)) {
            return false;
        }

        try {
            List<Permission> permissions = permissionMapper.findPermissionsByUserId(userId);
            return permissions.stream()
                    .anyMatch(permission -> permissionCode.equals(permission.getPermissionCode()));
        } catch (Exception e) {
            log.error("检查用户权限失败: userId={}, permissionCode={}, error={}", userId, permissionCode, e.getMessage());
            return false;
        }
    }

    @Override
    public List<String> getUserPermissions(Long userId) {
        if (userId == null) {
            return new ArrayList<>();
        }

        try {
            List<Permission> permissions = permissionMapper.findPermissionsByUserId(userId);
            return permissions.stream()
                    .map(Permission::getPermissionCode)
                    .collect(Collectors.toList());
        } catch (Exception e) {
            log.error("获取用户权限失败: userId={}, error={}", userId, e.getMessage());
            return new ArrayList<>();
        }
    }

    @Override
    public List<String> getUserRoles(Long userId) {
        if (userId == null) {
            return new ArrayList<>();
        }

        try {
            List<Role> roles = roleMapper.findRolesByUserId(userId);
            return roles.stream()
                    .map(Role::getRoleCode)
                    .collect(Collectors.toList());
        } catch (Exception e) {
            log.error("获取用户角色失败: userId={}, error={}", userId, e.getMessage());
            return new ArrayList<>();
        }
    }

    @Override
    @Transactional
    public Boolean assignRoles(Long userId, List<Long> roleIds) {
        if (userId == null || roleIds == null) {
            return false;
        }

        try {
            // 删除用户现有的角色
            userRoleMapper.deleteByUserId(userId);

            // 分配新角色
            for (Long roleId : roleIds) {
                UserRole userRole = new UserRole();
                userRole.setUserId(userId);
                userRole.setRoleId(roleId);
                userRoleMapper.insert(userRole);
            }

            log.info("用户角色分配成功: userId={}, roleIds={}", userId, roleIds);
            return true;
        } catch (Exception e) {
            log.error("用户角色分配失败: userId={}, roleIds={}, error={}", userId, roleIds, e.getMessage());
            return false;
        }
    }
}
