package com.jingzhenjili.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jingzhenjili.entity.Competition;
import com.jingzhenjili.vo.CompetitionVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 竞争Mapper接口
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
@Mapper
public interface CompetitionMapper extends BaseMapper<Competition> {

    /**
     * 分页查询竞争列表
     *
     * @param page 分页参数
     * @param userId 用户ID（可选）
     * @param status 状态（可选）
     * @param keyword 关键词（可选）
     * @param showAll 是否显示所有竞争
     * @param initiatorOnly 是否只显示用户发起的竞争
     * @param participantOnly 是否只显示用户参与的竞争
     * @return 竞争列表
     */
    @Select("<script>" +
            "SELECT c.id, c.title, c.description, c.initiator_id, c.participant_id, c.participant_task_id, " +
            "c.max_participants, c.current_participants, c.task_id, c.reward_type, c.reward_content, " +
            "c.start_time, c.end_time, c.status, c.winner_id, " +
            "COALESCE(c.initiator_completed, 0) as initiator_completed, " +
            "COALESCE(c.participant_completed, 0) as participant_completed, " +
            "COALESCE(c.initiator_confirmed, 0) as initiator_confirmed, " +
            "COALESCE(c.participant_confirmed, 0) as participant_confirmed, " +
            "c.initiator_completion_time, c.participant_completion_time, " +
            "c.created_time, c.updated_time, c.deleted, " +
            "t.title as task_title, t.category as task_category, t.difficulty as task_difficulty, " +
            "t.estimated_time as task_estimated_time, " +
            "pt.title as participant_task_title, pt.description as participant_task_description, " +
            "pt.category as participant_task_category, pt.priority as participant_task_priority, " +
            "pt.difficulty as participant_task_difficulty, pt.estimated_time as participant_task_estimated_time, " +
            "pt.reward_points as participant_task_reward_points, " +
            "u1.username as initiator_name, u1.nickname as initiator_nickname, u1.avatar as initiator_avatar, " +
            "u2.username as participant_name, u2.nickname as participant_nickname, u2.avatar as participant_avatar, " +
            "u3.username as winner_name, u3.nickname as winner_nickname " +
            "FROM competitions c " +
            "LEFT JOIN tasks t ON c.task_id = t.id " +
            "LEFT JOIN tasks pt ON c.participant_task_id = pt.id " +
            "LEFT JOIN users u1 ON c.initiator_id = u1.id " +
            "LEFT JOIN users u2 ON c.participant_id = u2.id " +
            "LEFT JOIN users u3 ON c.winner_id = u3.id " +
            "WHERE c.deleted = 0 " +
            "<choose>" +
            "<when test='showAll != null and showAll == true'>" +
            "<!-- 显示所有公开竞争，不限制用户 -->" +
            "</when>" +
            "<when test='initiatorOnly != null and initiatorOnly == true and userId != null'>" +
            "AND c.initiator_id = #{userId} " +
            "</when>" +
            "<when test='participantOnly != null and participantOnly == true and userId != null'>" +
            "AND c.participant_id = #{userId} " +
            "</when>" +
            "<when test='userId != null'>" +
            "AND (c.initiator_id = #{userId} OR c.participant_id = #{userId}) " +
            "</when>" +
            "</choose>" +
            "<if test='status != null'>" +
            "AND c.status = #{status} " +
            "</if>" +
            "<if test='keyword != null and keyword != \"\"'>" +
            "AND (c.title LIKE CONCAT('%', #{keyword}, '%') OR c.description LIKE CONCAT('%', #{keyword}, '%')) " +
            "</if>" +
            "ORDER BY c.created_time DESC" +
            "</script>")
    IPage<CompetitionVO> getCompetitionPage(Page<CompetitionVO> page,
                                          @Param("userId") Long userId,
                                          @Param("status") Integer status,
                                          @Param("keyword") String keyword,
                                          @Param("showAll") Boolean showAll,
                                          @Param("initiatorOnly") Boolean initiatorOnly,
                                          @Param("participantOnly") Boolean participantOnly);

    /**
     * 获取用户的竞争列表
     * 
     * @param userId 用户ID
     * @param status 状态（可选）
     * @return 竞争列表
     */
    @Select("<script>" +
            "SELECT c.id, c.title, c.description, c.initiator_id, c.participant_id, c.participant_task_id, " +
            "c.max_participants, c.current_participants, c.task_id, c.reward_type, c.reward_content, " +
            "c.start_time, c.end_time, c.status, c.winner_id, " +
            "COALESCE(c.initiator_completed, 0) as initiator_completed, " +
            "COALESCE(c.participant_completed, 0) as participant_completed, " +
            "COALESCE(c.initiator_confirmed, 0) as initiator_confirmed, " +
            "COALESCE(c.participant_confirmed, 0) as participant_confirmed, " +
            "c.initiator_completion_time, c.participant_completion_time, " +
            "c.created_time, c.updated_time, c.deleted, " +
            "t.title as task_title, t.category as task_category, t.difficulty as task_difficulty, " +
            "pt.title as participant_task_title, pt.description as participant_task_description, " +
            "pt.category as participant_task_category, pt.priority as participant_task_priority, " +
            "pt.difficulty as participant_task_difficulty, pt.estimated_time as participant_task_estimated_time, " +
            "pt.reward_points as participant_task_reward_points, " +
            "u1.username as initiator_name, u1.nickname as initiator_nickname, u1.avatar as initiator_avatar, " +
            "u2.username as participant_name, u2.nickname as participant_nickname, u2.avatar as participant_avatar, " +
            "u3.username as winner_name, u3.nickname as winner_nickname " +
            "FROM competitions c " +
            "LEFT JOIN tasks t ON c.task_id = t.id " +
            "LEFT JOIN tasks pt ON c.participant_task_id = pt.id " +
            "LEFT JOIN users u1 ON c.initiator_id = u1.id " +
            "LEFT JOIN users u2 ON c.participant_id = u2.id " +
            "LEFT JOIN users u3 ON c.winner_id = u3.id " +
            "WHERE c.deleted = 0 AND (c.initiator_id = #{userId} OR c.participant_id = #{userId}) " +
            "<if test='status != null'>" +
            "AND c.status = #{status} " +
            "</if>" +
            "ORDER BY c.created_time DESC" +
            "</script>")
    List<CompetitionVO> getUserCompetitions(@Param("userId") Long userId, @Param("status") Integer status);

    /**
     * 获取竞争详情
     * 
     * @param competitionId 竞争ID
     * @return 竞争详情
     */
    @Select("SELECT c.id, c.title, c.description, c.initiator_id, c.participant_id, c.participant_task_id, " +
            "c.max_participants, c.current_participants, c.task_id, c.reward_type, c.reward_content, " +
            "c.start_time, c.end_time, c.status, c.winner_id, " +
            "COALESCE(c.initiator_completed, 0) as initiator_completed, " +
            "COALESCE(c.participant_completed, 0) as participant_completed, " +
            "COALESCE(c.initiator_confirmed, 0) as initiator_confirmed, " +
            "COALESCE(c.participant_confirmed, 0) as participant_confirmed, " +
            "c.initiator_completion_time, c.participant_completion_time, " +
            "c.created_time, c.updated_time, c.deleted, " +
            "t.title as task_title, t.description as task_description, t.category as task_category, " +
            "t.priority as task_priority, t.difficulty as task_difficulty, t.estimated_time as task_estimated_time, " +
            "t.reward_points as task_reward_points, " +
            "pt.title as participant_task_title, pt.description as participant_task_description, " +
            "pt.category as participant_task_category, pt.priority as participant_task_priority, " +
            "pt.difficulty as participant_task_difficulty, pt.estimated_time as participant_task_estimated_time, " +
            "pt.reward_points as participant_task_reward_points, " +
            "u1.username as initiator_name, u1.nickname as initiator_nickname, u1.avatar as initiator_avatar, " +
            "u2.username as participant_name, u2.nickname as participant_nickname, u2.avatar as participant_avatar, " +
            "u3.username as winner_name, u3.nickname as winner_nickname " +
            "FROM competitions c " +
            "LEFT JOIN tasks t ON c.task_id = t.id " +
            "LEFT JOIN tasks pt ON c.participant_task_id = pt.id " +
            "LEFT JOIN users u1 ON c.initiator_id = u1.id " +
            "LEFT JOIN users u2 ON c.participant_id = u2.id " +
            "LEFT JOIN users u3 ON c.winner_id = u3.id " +
            "WHERE c.id = #{competitionId} AND c.deleted = 0")
    CompetitionVO getCompetitionDetail(@Param("competitionId") Long competitionId);

    /**
     * 获取用户竞争统计
     * 
     * @param userId 用户ID
     * @return 统计信息
     */
    @Select("SELECT " +
            "COUNT(*) as total, " +
            "SUM(CASE WHEN status = 0 THEN 1 ELSE 0 END) as pending, " +
            "SUM(CASE WHEN status = 1 THEN 1 ELSE 0 END) as ongoing, " +
            "SUM(CASE WHEN status = 2 THEN 1 ELSE 0 END) as completed, " +
            "SUM(CASE WHEN status = 3 THEN 1 ELSE 0 END) as cancelled, " +
            "SUM(CASE WHEN winner_id = #{userId} THEN 1 ELSE 0 END) as won " +
            "FROM competitions " +
            "WHERE (initiator_id = #{userId} OR participant_id = #{userId}) AND deleted = 0")
    CompetitionStatistics getUserCompetitionStatistics(@Param("userId") Long userId);

    /**
     * 根据任务ID获取相关竞争
     *
     * @param taskId 任务ID
     * @return 竞争列表
     */
    @Select("SELECT c.id, c.title, c.description, c.initiator_id, c.participant_id, c.participant_task_id, " +
            "c.max_participants, c.current_participants, c.task_id, c.reward_type, c.reward_content, " +
            "c.start_time, c.end_time, c.status, c.winner_id, " +
            "COALESCE(c.initiator_completed, 0) as initiator_completed, " +
            "COALESCE(c.participant_completed, 0) as participant_completed, " +
            "COALESCE(c.initiator_confirmed, 0) as initiator_confirmed, " +
            "COALESCE(c.participant_confirmed, 0) as participant_confirmed, " +
            "c.initiator_completion_time, c.participant_completion_time, " +
            "c.created_time, c.updated_time, c.deleted, " +
            "t.title as task_title, t.category as task_category, t.difficulty as task_difficulty, " +
            "t.estimated_time as task_estimated_time, " +
            "pt.title as participant_task_title, pt.description as participant_task_description, " +
            "pt.category as participant_task_category, pt.priority as participant_task_priority, " +
            "pt.difficulty as participant_task_difficulty, pt.estimated_time as participant_task_estimated_time, " +
            "pt.reward_points as participant_task_reward_points, " +
            "u1.username as initiator_name, u1.nickname as initiator_nickname, u1.avatar as initiator_avatar, " +
            "u2.username as participant_name, u2.nickname as participant_nickname, u2.avatar as participant_avatar, " +
            "u3.username as winner_name, u3.nickname as winner_nickname " +
            "FROM competitions c " +
            "LEFT JOIN tasks t ON c.task_id = t.id " +
            "LEFT JOIN tasks pt ON c.participant_task_id = pt.id " +
            "LEFT JOIN users u1 ON c.initiator_id = u1.id " +
            "LEFT JOIN users u2 ON c.participant_id = u2.id " +
            "LEFT JOIN users u3 ON c.winner_id = u3.id " +
            "WHERE c.deleted = 0 AND (c.task_id = #{taskId} OR c.participant_task_id = #{taskId}) " +
            "ORDER BY c.created_time DESC")
    List<CompetitionVO> getCompetitionsByTaskId(@Param("taskId") Long taskId);

    /**
     * 检查用户是否可以创建竞争（与指定用户和任务）
     *
     * @param initiatorId 发起者ID
     * @param participantId 参与者ID（可为空）
     * @param taskId 任务ID
     * @return 是否存在进行中的竞争
     */
    @Select("<script>" +
            "SELECT COUNT(*) > 0 FROM competitions " +
            "WHERE " +
            "<if test='participantId != null'>" +
            "((initiator_id = #{initiatorId} AND participant_id = #{participantId}) " +
            "OR (initiator_id = #{participantId} AND participant_id = #{initiatorId})) " +
            "</if>" +
            "<if test='participantId == null'>" +
            "initiator_id = #{initiatorId} " +
            "</if>" +
            "AND task_id = #{taskId} AND status IN (0, 1) AND deleted = 0" +
            "</script>")
    Boolean hasOngoingCompetition(@Param("initiatorId") Long initiatorId,
                                 @Param("participantId") Long participantId,
                                 @Param("taskId") Long taskId);

    /**
     * 获取任务的竞争数量
     *
     * @param taskId 任务ID
     * @return 竞争数量
     */
    @Select("SELECT " +
            "(SELECT COUNT(*) FROM competitions WHERE task_id = #{taskId} AND deleted = 0) + " +
            "(SELECT COUNT(*) FROM competitions WHERE participant_task_id = #{taskId} AND deleted = 0) " +
            "as competition_count")
    Integer getCompetitionCountByTaskId(@Param("taskId") Long taskId);

    /**
     * 竞争统计信息内部类
     */
    class CompetitionStatistics {
        private Long total;
        private Long pending;
        private Long ongoing;
        private Long completed;
        private Long cancelled;
        private Long won;

        // getters and setters
        public Long getTotal() { return total; }
        public void setTotal(Long total) { this.total = total; }
        public Long getPending() { return pending; }
        public void setPending(Long pending) { this.pending = pending; }
        public Long getOngoing() { return ongoing; }
        public void setOngoing(Long ongoing) { this.ongoing = ongoing; }
        public Long getCompleted() { return completed; }
        public void setCompleted(Long completed) { this.completed = completed; }
        public Long getCancelled() { return cancelled; }
        public void setCancelled(Long cancelled) { this.cancelled = cancelled; }
        public Long getWon() { return won; }
        public void setWon(Long won) { this.won = won; }
    }
}
