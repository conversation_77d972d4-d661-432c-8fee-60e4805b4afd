package com.jingzhenjili.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 用户积分实体类
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("user_points")
public class UserPoints implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 积分记录ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 用户ID
     */
    @TableField("user_id")
    private Long userId;

    /**
     * 积分变化
     */
    @TableField("points")
    private Integer points;

    /**
     * 类型：1-获得，2-消费
     */
    @TableField("type")
    private Integer type;

    /**
     * 来源：task_completion-任务完成，competition_win-竞争获胜
     */
    @TableField("source")
    private String source;

    /**
     * 来源ID
     */
    @TableField("source_id")
    private Long sourceId;

    /**
     * 描述
     */
    @TableField("description")
    private String description;

    /**
     * 创建时间
     */
    @TableField(value = "created_time", fill = FieldFill.INSERT)
    private LocalDateTime createdTime;

    /**
     * 是否删除：0-否，1-是
     */
    @TableLogic
    @TableField("deleted")
    private Integer deleted;
}
