<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="ChangeListManager">
    <list default="true" id="baa941a3-4a9d-41a0-830d-6c3e3ab0b932" name="Changes" comment="" />
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="MarkdownSettingsMigration">
    <option name="stateVersion" value="1" />
  </component>
  <component name="ProjectId" id="2z8sU8UxTq27gvsZZmZFEoXIKvN" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">
    <property name="RunOnceActivity.OpenProjectViewOnStart" value="true" />
    <property name="RunOnceActivity.ShowReadmeOnStart" value="true" />
    <property name="WebServerToolWindowFactoryState" value="false" />
    <property name="nodejs_package_manager_path" value="npm" />
    <property name="vue.rearranger.settings.migration" value="true" />
  </component>
  <component name="RunManager">
    <configuration name="serve" type="js.build_tools.npm" temporary="true" nameIsGenerated="true">
      <package-json value="$PROJECT_DIR$/package.json" />
      <command value="run" />
      <scripts>
        <script value="serve" />
      </scripts>
      <node-interpreter value="project" />
      <envs />
      <method v="2" />
    </configuration>
    <recent_temporary>
      <list>
        <item itemvalue="npm.serve" />
      </list>
    </recent_temporary>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="application-level" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="baa941a3-4a9d-41a0-830d-6c3e3ab0b932" name="Changes" comment="" />
      <created>1751124052323</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1751124052323</updated>
      <workItem from="1751124053895" duration="1092000" />
      <workItem from="1752973922320" duration="2231000" />
      <workItem from="1753102008154" duration="784000" />
      <workItem from="1753172812784" duration="199000" />
      <workItem from="1753326276864" duration="30000" />
      <workItem from="1754122607303" duration="2421000" />
      <workItem from="1754701838343" duration="2657000" />
      <workItem from="1754802730507" duration="4261000" />
      <workItem from="1754880757322" duration="833000" />
    </task>
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
</project>