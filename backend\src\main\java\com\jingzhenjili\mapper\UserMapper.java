package com.jingzhenjili.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jingzhenjili.entity.User;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 用户Mapper接口
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
@Mapper
public interface UserMapper extends BaseMapper<User> {

    /**
     * 根据用户名查询用户
     * 
     * @param username 用户名
     * @return 用户信息
     */
    @Select("SELECT * FROM users WHERE username = #{username} AND deleted = 0")
    User findByUsername(@Param("username") String username);

    /**
     * 根据邮箱查询用户
     * 
     * @param email 邮箱
     * @return 用户信息
     */
    @Select("SELECT * FROM users WHERE email = #{email} AND deleted = 0")
    User findByEmail(@Param("email") String email);

    /**
     * 根据手机号查询用户
     * 
     * @param phone 手机号
     * @return 用户信息
     */
    @Select("SELECT * FROM users WHERE phone = #{phone} AND deleted = 0")
    User findByPhone(@Param("phone") String phone);

    /**
     * 搜索用户（根据用户名或昵称）
     * 
     * @param keyword 关键词
     * @param limit 限制数量
     * @return 用户列表
     */
    @Select("SELECT * FROM users WHERE (username LIKE CONCAT('%', #{keyword}, '%') OR nickname LIKE CONCAT('%', #{keyword}, '%')) AND status = 1 AND deleted = 0 LIMIT #{limit}")
    List<User> searchUsers(@Param("keyword") String keyword, @Param("limit") Integer limit);

    /**
     * 获取用户统计信息
     *
     * @return 统计信息
     */
    @Select("SELECT COUNT(*) as total, " +
            "SUM(CASE WHEN status = 1 THEN 1 ELSE 0 END) as active, " +
            "SUM(CASE WHEN status = 0 THEN 1 ELSE 0 END) as inactive " +
            "FROM users WHERE deleted = 0")
    UserStatistics getUserStatistics();

    /**
     * 获取用户完成任务数量
     *
     * @param userId 用户ID
     * @return 完成任务数量
     */
    @Select("SELECT COUNT(DISTINCT tc.task_id) " +
            "FROM task_completions tc " +
            "WHERE tc.user_id = #{userId} AND tc.self_confirmed = 1 AND tc.peer_confirmed = 1 AND tc.deleted = 0")
    Integer getUserCompletedTaskCount(@Param("userId") Long userId);

    /**
     * 获取用户竞争胜利次数
     *
     * @param userId 用户ID
     * @return 竞争胜利次数
     */
    @Select("SELECT COUNT(*) " +
            "FROM competitions c " +
            "WHERE c.winner_id = #{userId} AND c.status = 2 AND c.deleted = 0")
    Integer getUserWinCount(@Param("userId") Long userId);

    /**
     * 用户统计信息内部类
     */
    class UserStatistics {
        private Long total;
        private Long active;
        private Long inactive;

        // getters and setters
        public Long getTotal() { return total; }
        public void setTotal(Long total) { this.total = total; }
        public Long getActive() { return active; }
        public void setActive(Long active) { this.active = active; }
        public Long getInactive() { return inactive; }
        public void setInactive(Long inactive) { this.inactive = inactive; }
    }
}
