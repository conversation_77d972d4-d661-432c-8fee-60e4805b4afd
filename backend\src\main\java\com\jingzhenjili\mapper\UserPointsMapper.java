package com.jingzhenjili.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jingzhenjili.entity.UserPoints;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 用户积分Mapper接口
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
@Mapper
public interface UserPointsMapper extends BaseMapper<UserPoints> {

    /**
     * 获取用户总积分
     * 
     * @param userId 用户ID
     * @return 总积分
     */
    @Select("SELECT COALESCE(SUM(CASE WHEN type = 1 THEN points ELSE -points END), 0) " +
            "FROM user_points WHERE user_id = #{userId} AND deleted = 0")
    Integer getUserTotalPoints(@Param("userId") Long userId);

    /**
     * 获取用户积分记录列表
     * 
     * @param userId 用户ID
     * @param limit 限制数量
     * @return 积分记录列表
     */
    @Select("SELECT * FROM user_points WHERE user_id = #{userId} AND deleted = 0 " +
            "ORDER BY created_time DESC LIMIT #{limit}")
    List<UserPoints> getUserPointsHistory(@Param("userId") Long userId, @Param("limit") Integer limit);

    /**
     * 获取用户积分统计
     * 
     * @param userId 用户ID
     * @return 积分统计信息
     */
    @Select("SELECT " +
            "COALESCE(SUM(CASE WHEN type = 1 THEN points ELSE 0 END), 0) as totalEarned, " +
            "COALESCE(SUM(CASE WHEN type = 2 THEN points ELSE 0 END), 0) as totalSpent, " +
            "COALESCE(SUM(CASE WHEN type = 1 THEN points ELSE -points END), 0) as totalPoints, " +
            "COUNT(*) as recordCount " +
            "FROM user_points WHERE user_id = #{userId} AND deleted = 0")
    UserPointsStatistics getUserPointsStatistics(@Param("userId") Long userId);

    /**
     * 用户积分统计信息内部类
     */
    class UserPointsStatistics {
        private Integer totalEarned;
        private Integer totalSpent;
        private Integer totalPoints;
        private Integer recordCount;

        // Getters and Setters
        public Integer getTotalEarned() {
            return totalEarned;
        }

        public void setTotalEarned(Integer totalEarned) {
            this.totalEarned = totalEarned;
        }

        public Integer getTotalSpent() {
            return totalSpent;
        }

        public void setTotalSpent(Integer totalSpent) {
            this.totalSpent = totalSpent;
        }

        public Integer getTotalPoints() {
            return totalPoints;
        }

        public void setTotalPoints(Integer totalPoints) {
            this.totalPoints = totalPoints;
        }

        public Integer getRecordCount() {
            return recordCount;
        }

        public void setRecordCount(Integer recordCount) {
            this.recordCount = recordCount;
        }
    }
}
