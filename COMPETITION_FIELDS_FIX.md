# 竞争表字段缺失问题修复

## 问题描述

在访问 `/api/competition/11/join` 接口时出现以下错误：

```
### Error querying database.  Cause: java.sql.SQLSyntaxErrorException: Unknown column 'initiator_completion_time' in 'field list'
### The error may exist in com/jingzhenjili/mapper/CompetitionMapper.java (best guess)
```

## 问题原因

1. **数据库表结构不完整**：`competitions` 表缺少以下字段：
   - `initiator_completed` - 发起者是否完成任务
   - `participant_completed` - 参与者是否完成任务
   - `initiator_confirmed` - 发起者是否确认对方完成
   - `participant_confirmed` - 参与者是否确认对方完成
   - `initiator_completion_time` - 发起者完成时间
   - `participant_completion_time` - 参与者完成时间

2. **Mapper查询使用了 `c.*`**：CompetitionMapper中的查询使用了 `SELECT c.*`，这会尝试选择所有字段，包括实体类中定义但数据库中不存在的字段。

## 解决方案

### 1. 修改Mapper查询（已完成）

将CompetitionMapper中所有使用 `c.*` 的查询改为明确指定字段名，并使用 `COALESCE` 函数处理可能不存在的字段：

```sql
SELECT c.id, c.title, c.description, c.initiator_id, c.participant_id, c.participant_task_id,
       c.max_participants, c.current_participants, c.task_id, c.reward_type, c.reward_content,
       c.start_time, c.end_time, c.status, c.winner_id,
       COALESCE(c.initiator_completed, 0) as initiator_completed,
       COALESCE(c.participant_completed, 0) as participant_completed,
       COALESCE(c.initiator_confirmed, 0) as initiator_confirmed,
       COALESCE(c.participant_confirmed, 0) as participant_confirmed,
       c.initiator_completion_time, c.participant_completion_time,
       c.created_time, c.updated_time, c.deleted,
       -- 其他JOIN字段...
```

### 2. 数据库迁移脚本

创建了 `database/fix_competition_fields.sql` 脚本来添加缺失的字段：

```sql
-- 添加发起者完成状态字段
ALTER TABLE competitions ADD COLUMN initiator_completed tinyint(1) DEFAULT 0 COMMENT '发起者是否完成任务';

-- 添加参与者完成状态字段
ALTER TABLE competitions ADD COLUMN participant_completed tinyint(1) DEFAULT 0 COMMENT '参与者是否完成任务';

-- 添加发起者确认状态字段
ALTER TABLE competitions ADD COLUMN initiator_confirmed tinyint(1) DEFAULT 0 COMMENT '发起者是否确认对方完成';

-- 添加参与者确认状态字段
ALTER TABLE competitions ADD COLUMN participant_confirmed tinyint(1) DEFAULT 0 COMMENT '参与者是否确认对方完成';

-- 添加发起者完成时间字段
ALTER TABLE competitions ADD COLUMN initiator_completion_time datetime DEFAULT NULL COMMENT '发起者完成时间';

-- 添加参与者完成时间字段
ALTER TABLE competitions ADD COLUMN participant_completion_time datetime DEFAULT NULL COMMENT '参与者完成时间';
```

## 执行步骤

1. **立即修复**：已修改CompetitionMapper查询，使用 `COALESCE` 函数处理不存在的字段，这样即使字段不存在也不会报错。

2. **数据库更新**：运行 `database/fix_competition_fields.sql` 脚本来添加缺失的字段。

3. **验证修复**：
   - 重启应用
   - 测试 `/api/competition/11/join` 接口
   - 检查其他竞争相关接口

## 修改的文件

- `backend/src/main/java/com/jingzhenjili/mapper/CompetitionMapper.java` - 修改了所有使用 `c.*` 的查询
- `database/fix_competition_fields.sql` - 新增的数据库迁移脚本

## 注意事项

1. 使用 `COALESCE` 函数确保即使字段不存在也能返回默认值
2. 所有新增字段都有合适的默认值
3. 保持了向后兼容性
4. 建议在生产环境执行数据库迁移前先备份数据

## 测试验证

修复后应该测试以下功能：
- 竞争列表查询
- 竞争详情查询
- 参与竞争
- 完成任务
- 确认完成

所有这些功能现在都应该正常工作，不再出现字段不存在的错误。
