-- 修复竞争表缺失字段
-- 这个脚本会添加缺失的字段（如果不存在的话）

-- 添加发起者完成状态字段
-- 如果字段已存在，这个语句会失败但不会影响其他操作
ALTER TABLE competitions ADD COLUMN initiator_completed tinyint(1) DEFAULT 0 COMMENT '发起者是否完成任务';

-- 添加参与者完成状态字段
ALTER TABLE competitions ADD COLUMN participant_completed tinyint(1) DEFAULT 0 COMMENT '参与者是否完成任务';

-- 添加发起者确认状态字段
ALTER TABLE competitions ADD COLUMN initiator_confirmed tinyint(1) DEFAULT 0 COMMENT '发起者是否确认对方完成';

-- 添加参与者确认状态字段
ALTER TABLE competitions ADD COLUMN participant_confirmed tinyint(1) DEFAULT 0 COMMENT '参与者是否确认对方完成';

-- 添加发起者完成时间字段
ALTER TABLE competitions ADD COLUMN initiator_completion_time datetime DEFAULT NULL COMMENT '发起者完成时间';

-- 添加参与者完成时间字段
ALTER TABLE competitions ADD COLUMN participant_completion_time datetime DEFAULT NULL COMMENT '参与者完成时间';

-- 验证表结构
SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE, COLUMN_DEFAULT
FROM INFORMATION_SCHEMA.COLUMNS
WHERE TABLE_NAME = 'competitions'
AND COLUMN_NAME IN ('initiator_completed', 'participant_completed', 'initiator_confirmed', 'participant_confirmed', 'initiator_completion_time', 'participant_completion_time')
ORDER BY ORDINAL_POSITION;
