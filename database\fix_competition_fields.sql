-- 修复竞争表缺失字段
-- 这个脚本会检查并添加缺失的字段

-- 检查并添加发起者完成状态字段
ALTER TABLE competitions 
ADD COLUMN IF NOT EXISTS initiator_completed tinyint(1) DEFAULT 0 COMMENT '发起者是否完成任务';

-- 检查并添加参与者完成状态字段
ALTER TABLE competitions 
ADD COLUMN IF NOT EXISTS participant_completed tinyint(1) DEFAULT 0 COMMENT '参与者是否完成任务';

-- 检查并添加发起者确认状态字段
ALTER TABLE competitions 
ADD COLUMN IF NOT EXISTS initiator_confirmed tinyint(1) DEFAULT 0 COMMENT '发起者是否确认对方完成';

-- 检查并添加参与者确认状态字段
ALTER TABLE competitions 
ADD COLUMN IF NOT EXISTS participant_confirmed tinyint(1) DEFAULT 0 COMMENT '参与者是否确认对方完成';

-- 检查并添加发起者完成时间字段
ALTER TABLE competitions 
ADD COLUMN IF NOT EXISTS initiator_completion_time datetime DEFAULT NULL COMMENT '发起者完成时间';

-- 检查并添加参与者完成时间字段
ALTER TABLE competitions 
ADD COLUMN IF NOT EXISTS participant_completion_time datetime DEFAULT NULL COMMENT '参与者完成时间';

-- 验证表结构
DESCRIBE competitions;
